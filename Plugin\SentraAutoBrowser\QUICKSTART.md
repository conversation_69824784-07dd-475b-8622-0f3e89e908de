# 🚀 Sentra Auto Browser 插件快速启动指南

## 📦 5分钟快速安装

### 1. 自动安装 Sentra Auto Browser

**Windows 用户**:
```bash
cd Plugin/SentraAutoBrowser
install-sentra.bat
```

**Linux/Mac 用户**:
```bash
cd Plugin/SentraAutoBrowser
chmod +x install-sentra.js
node install-sentra.js install
```

### 2. 配置 API 密钥

复制配置文件：
```bash
cp config.env.example config.env
```

编辑 `config.env`，至少填写一个 AI 提供商的密钥：

```env
# OpenAI (推荐)
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4o-mini

# 或者 Google Gemini
GOOGLE_API_KEY=your-google-api-key-here
GOOGLE_MODEL=gemini-1.5-pro

# 或者 Anthropic Claude
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
```

### 3. 测试安装

```bash
node test.js
```

### 4. 启用插件

在你的 VCP 系统提示词中添加：
```
{{VCPSentraAutoBrowser}}
```

重启 VCP 服务器。

## 🎮 立即开始使用

### 基础示例

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SentraAutoBrowser「末」,
task:「始」打开百度，搜索人工智能，点击第一个结果「末」
<<<[END_TOOL_REQUEST]>>>
```

### 视频网站自动化

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SentraAutoBrowser「末」,
task:「始」打开bilibili，搜索编程教程，播放播放量最高的视频「末」,
max_steps:「始」10「末」
<<<[END_TOOL_REQUEST]>>>
```

### 电商购物自动化

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SentraAutoBrowser「末」,
task:「始」打开淘宝，搜索蓝牙耳机，筛选价格100-300元，查看评价最好的商品「末」,
max_steps:「始」15「末」,
debug:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

## ⚡ 常用配置

### 无头模式（后台运行）
```
headless:「始」true「末」
```

### 调试模式（查看详细过程）
```
debug:「始」true「末」
```

### 指定 AI 模型
```
provider:「始」openai「末」,
model:「始」gpt-4o「末」
```

### 增加执行步数
```
max_steps:「始」30「末」
```

## 🔧 故障排除

### 问题1: 插件未找到
**解决方案**: 确保已正确安装
```bash
node install-sentra.js check
```

### 问题2: API 密钥错误
**解决方案**: 检查 `config.env` 文件中的密钥是否正确

### 问题3: 浏览器启动失败
**解决方案**: 安装浏览器依赖
```bash
cd sentra-auto-browser
npx playwright install
```

### 问题4: 任务执行超时
**解决方案**: 增加超时时间或步数
```env
BROWSER_TIMEOUT=60000
MAX_STEPS=30
```

## 📞 获取帮助

- 查看完整文档: [README.md](README.md)
- 运行测试: `node test.js`
- 检查安装: `node install-sentra.js check`
- VCP 项目主页: https://github.com/lioensky/VCPToolBox

## 🎯 下一步

1. 尝试更复杂的自动化任务
2. 探索不同的 AI 模型
3. 自定义浏览器配置
4. 集成到你的工作流中

开始享受 AI 驱动的浏览器自动化吧！🚀
