{"name": "vcp-sentra-auto-browser", "version": "1.0.0", "description": "Sentra Auto Browser VCP Plugin - AI驱动的智能浏览器自动化插件", "main": "sentra-browser.js", "scripts": {"install-sentra": "node install-sentra.js install", "uninstall-sentra": "node install-sentra.js uninstall", "check-sentra": "node install-sentra.js check", "check": "node check-installation.js", "test": "node test.js", "setup": "npm run install-sentra && npm run check"}, "keywords": ["vcp", "plugin", "browser", "automation", "ai", "sentra"], "author": "VCP Team", "license": "CC-BY-NC-SA-4.0", "engines": {"node": ">=16.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/lioensky/VCPToolBox.git", "directory": "Plugin/SentraAutoBrowser"}, "bugs": {"url": "https://github.com/lioensky/VCPToolBox/issues"}, "homepage": "https://github.com/lioensky/VCPToolBox#readme"}