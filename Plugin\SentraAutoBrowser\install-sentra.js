#!/usr/bin/env node

/**
 * Sentra Auto Browser 安装脚本
 * 自动下载并安装 Sentra Auto Browser
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');
const https = require('https');

const SENTRA_REPO = 'https://github.com/JustForSO/Sentra-Auto-Browser.git';
const INSTALL_DIR = path.join(__dirname, 'sentra-auto-browser');

// 检查是否已安装
function checkInstallation() {
    return fs.existsSync(path.join(INSTALL_DIR, 'package.json'));
}

// 执行命令
function executeCommand(command, cwd = process.cwd()) {
    return new Promise((resolve, reject) => {
        console.log(`执行命令: ${command}`);
        exec(command, { cwd }, (error, stdout, stderr) => {
            if (error) {
                console.error(`命令执行失败: ${error.message}`);
                reject(error);
                return;
            }
            if (stderr) {
                console.warn(`警告: ${stderr}`);
            }
            console.log(stdout);
            resolve(stdout);
        });
    });
}

// 克隆仓库
async function cloneRepository() {
    console.log('正在克隆 Sentra Auto Browser 仓库...');
    
    if (fs.existsSync(INSTALL_DIR)) {
        console.log('删除现有安装目录...');
        await executeCommand(`rm -rf "${INSTALL_DIR}"`);
    }
    
    await executeCommand(`git clone ${SENTRA_REPO} "${INSTALL_DIR}"`);
    console.log('仓库克隆完成！');
}

// 安装依赖
async function installDependencies() {
    console.log('正在安装 Node.js 依赖...');
    await executeCommand('npm install', INSTALL_DIR);
    
    console.log('正在编译项目...');
    await executeCommand('npm run build', INSTALL_DIR);
    
    console.log('正在安装浏览器...');
    await executeCommand('npx playwright install', INSTALL_DIR);
    
    console.log('依赖安装完成！');
}

// 创建配置文件
function createConfig() {
    console.log('正在创建配置文件...');
    
    const configPath = path.join(INSTALL_DIR, '.env');
    const localConfigPath = path.join(__dirname, 'config.env');
    
    let configContent = '';
    
    // 读取本地配置
    if (fs.existsSync(localConfigPath)) {
        const localConfig = fs.readFileSync(localConfigPath, 'utf-8');
        const lines = localConfig.split('\n');
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed && !trimmed.startsWith('#')) {
                configContent += line + '\n';
            }
        }
    }
    
    // 如果没有本地配置，使用默认配置
    if (!configContent) {
        configContent = `# Sentra Auto Browser 配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

BROWSER_HEADLESS=false
BROWSER_VIEWPORT_WIDTH=1280
BROWSER_VIEWPORT_HEIGHT=720
BROWSER_TIMEOUT=30000

LOG_LEVEL=info
DEBUG=false
`;
    }
    
    fs.writeFileSync(configPath, configContent);
    console.log('配置文件创建完成！');
}

// 测试安装
async function testInstallation() {
    console.log('正在测试安装...');
    
    try {
        await executeCommand('npx sentra-auto config', INSTALL_DIR);
        console.log('✅ Sentra Auto Browser 安装成功！');
        return true;
    } catch (error) {
        console.error('❌ 安装测试失败:', error.message);
        return false;
    }
}

// 主安装函数
async function install() {
    try {
        console.log('🚀 开始安装 Sentra Auto Browser...\n');
        
        // 检查是否已安装
        if (checkInstallation()) {
            console.log('⚠️  检测到已有安装，将重新安装...\n');
        }
        
        // 检查系统要求
        console.log('检查系统要求...');
        
        // 检查 Node.js
        try {
            await executeCommand('node --version');
        } catch (error) {
            throw new Error('需要安装 Node.js (版本 >= 16)');
        }
        
        // 检查 Git
        try {
            await executeCommand('git --version');
        } catch (error) {
            throw new Error('需要安装 Git');
        }
        
        console.log('✅ 系统要求检查通过\n');
        
        // 执行安装步骤
        await cloneRepository();
        await installDependencies();
        createConfig();
        
        // 测试安装
        const success = await testInstallation();
        
        if (success) {
            console.log('\n🎉 Sentra Auto Browser 安装完成！');
            console.log('\n📝 下一步:');
            console.log('1. 编辑 config.env 文件，填写你的 AI API 密钥');
            console.log('2. 重启 VCP 服务器以加载新插件');
            console.log('3. 在系统提示词中添加 {{VCPSentraAutoBrowser}} 来启用插件');
        } else {
            console.log('\n❌ 安装过程中出现问题，请检查错误信息');
        }
        
    } catch (error) {
        console.error('\n❌ 安装失败:', error.message);
        console.log('\n🔧 故障排除建议:');
        console.log('1. 确保网络连接正常');
        console.log('2. 确保已安装 Node.js (>= 16) 和 Git');
        console.log('3. 确保有足够的磁盘空间');
        console.log('4. 检查防火墙和代理设置');
        process.exit(1);
    }
}

// 卸载函数
async function uninstall() {
    try {
        console.log('🗑️  正在卸载 Sentra Auto Browser...');
        
        if (fs.existsSync(INSTALL_DIR)) {
            await executeCommand(`rm -rf "${INSTALL_DIR}"`);
            console.log('✅ 卸载完成！');
        } else {
            console.log('⚠️  未找到安装目录，可能已经卸载');
        }
    } catch (error) {
        console.error('❌ 卸载失败:', error.message);
        process.exit(1);
    }
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
    case 'install':
        install();
        break;
    case 'uninstall':
        uninstall();
        break;
    case 'check':
        if (checkInstallation()) {
            console.log('✅ Sentra Auto Browser 已安装');
        } else {
            console.log('❌ Sentra Auto Browser 未安装');
        }
        break;
    default:
        console.log('Sentra Auto Browser 安装脚本');
        console.log('');
        console.log('用法:');
        console.log('  node install-sentra.js install   - 安装 Sentra Auto Browser');
        console.log('  node install-sentra.js uninstall - 卸载 Sentra Auto Browser');
        console.log('  node install-sentra.js check     - 检查安装状态');
        break;
}
