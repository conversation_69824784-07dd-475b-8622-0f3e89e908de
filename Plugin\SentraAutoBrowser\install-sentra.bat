@echo off
chcp 65001 >nul
echo 🚀 Sentra Auto Browser 安装脚本
echo.

:: 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Node.js
    echo 请先安装 Node.js (版本 >= 16): https://nodejs.org/
    pause
    exit /b 1
)

:: 检查 Git
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Git
    echo 请先安装 Git: https://git-scm.com/
    pause
    exit /b 1
)

echo ✅ 系统要求检查通过
echo.

:: 执行安装
echo 正在执行安装...
node install-sentra.js install

if errorlevel 1 (
    echo.
    echo ❌ 安装失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 🎉 安装完成！
echo.
echo 📝 下一步:
echo 1. 编辑 config.env 文件，填写你的 AI API 密钥
echo 2. 重启 VCP 服务器以加载新插件
echo 3. 在系统提示词中添加 {{VCPSentraAutoBrowser}} 来启用插件
echo.
pause
