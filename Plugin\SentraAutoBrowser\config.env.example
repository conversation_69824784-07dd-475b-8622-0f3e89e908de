# Sentra Auto Browser 插件配置示例
# 复制此文件为 config.env 并填写实际的配置值

# -------------------------------------------------------------------
# [AI模型配置] - 至少配置一个AI提供商
# -------------------------------------------------------------------

# OpenAI配置 (推荐)
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

# Google Gemini配置 (可选)
# GOOGLE_API_KEY=your-google-api-key-here
# GOOGLE_MODEL=gemini-1.5-pro

# Anthropic <PERSON>配置 (可选)
# ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here
# ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# -------------------------------------------------------------------
# [浏览器配置]
# -------------------------------------------------------------------

# 浏览器显示模式
BROWSER_HEADLESS=false

# 浏览器视窗大小
BROWSER_VIEWPORT_WIDTH=1280
BROWSER_VIEWPORT_HEIGHT=720

# 超时设置（毫秒）
BROWSER_TIMEOUT=30000

# -------------------------------------------------------------------
# [AI代理配置]
# -------------------------------------------------------------------

# 最大执行步数
MAX_STEPS=20

# 是否启用视觉功能（AI可以"看到"页面截图）
ENABLE_VISION=true

# 调试模式
DEBUG_MODE=false

# -------------------------------------------------------------------
# [高级配置]
# -------------------------------------------------------------------

# 用户数据目录（保持登录状态）
# BROWSER_USER_DATA_DIR=./user-data

# CDP连接配置（连接到现有浏览器）
# BROWSER_CONNECT_TO_USER_BROWSER=false
# BROWSER_DEBUG_HOST=localhost
# BROWSER_DEBUG_PORT=9222

# 反检测模式
# BROWSER_STEALTH_MODE=true
