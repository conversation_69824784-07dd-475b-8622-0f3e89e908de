#!/usr/bin/env node

/**
 * 检查 Sentra Auto Browser 安装状态
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function checkLocalInstallation() {
    const sentraPath = path.join(__dirname, 'sentra-auto-browser');
    const packagePath = path.join(sentraPath, 'package.json');
    const srcPath = path.join(sentraPath, 'src');
    
    return fs.existsSync(packagePath) && fs.existsSync(srcPath);
}

function checkGlobalInstallation() {
    try {
        execSync('npx sentra-auto --version', { stdio: 'ignore' });
        return true;
    } catch (error) {
        return false;
    }
}

function checkConfig() {
    const configPath = path.join(__dirname, 'config.env');
    if (!fs.existsSync(configPath)) {
        return { exists: false, hasApiKey: false };
    }
    
    const config = fs.readFileSync(configPath, 'utf-8');
    const hasApiKey = config.includes('OPENAI_API_KEY=sk-') || 
                     config.includes('GOOGLE_API_KEY=') || 
                     config.includes('ANTHROPIC_API_KEY=sk-ant-');
    
    return { exists: true, hasApiKey };
}

function main() {
    console.log('🔍 检查 Sentra Auto Browser 安装状态\n');
    
    // 检查本地安装
    const localInstalled = checkLocalInstallation();
    console.log(`📦 本地安装: ${localInstalled ? '✅ 已安装' : '❌ 未安装'}`);
    
    // 检查全局安装
    const globalInstalled = checkGlobalInstallation();
    console.log(`🌐 全局安装: ${globalInstalled ? '✅ 可用' : '❌ 不可用'}`);
    
    // 检查配置
    const config = checkConfig();
    console.log(`⚙️  配置文件: ${config.exists ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`🔑 API密钥: ${config.hasApiKey ? '✅ 已配置' : '❌ 未配置'}`);
    
    console.log('\n📋 总体状态:');
    
    if ((localInstalled || globalInstalled) && config.exists && config.hasApiKey) {
        console.log('✅ 插件已就绪，可以正常使用！');
        return 0;
    } else {
        console.log('❌ 插件未就绪，需要进行配置');
        console.log('\n🔧 解决步骤:');
        
        if (!localInstalled && !globalInstalled) {
            console.log('1. 安装 Sentra Auto Browser:');
            console.log('   Windows: install-sentra.bat');
            console.log('   Linux/Mac: node install-sentra.js install');
        }
        
        if (!config.exists) {
            console.log('2. 创建配置文件:');
            console.log('   cp config.env.example config.env');
        }
        
        if (!config.hasApiKey) {
            console.log('3. 配置 AI API 密钥:');
            console.log('   编辑 config.env 文件，填写 OPENAI_API_KEY 等');
        }
        
        console.log('4. 重启 VCP 服务器');
        console.log('5. 在系统提示词中添加 {{VCPSentraAutoBrowser}}');
        
        return 1;
    }
}

if (require.main === module) {
    process.exit(main());
}

module.exports = { checkLocalInstallation, checkGlobalInstallation, checkConfig };
