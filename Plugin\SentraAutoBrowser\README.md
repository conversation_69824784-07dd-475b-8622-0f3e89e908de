# Sentra Auto Browser VCP 插件

基于AI驱动的智能浏览器自动化插件，集成了 [Sentra Auto Browser](https://github.com/JustForSO/Sentra-Auto-Browser) 的强大功能。

## 🚀 功能特色

- **🧠 AI驱动**: 使用大语言模型进行智能决策，无需编写复杂的选择器
- **🎯 精准定位**: 智能DOM元素检测，自动识别可交互元素  
- **🔄 实时适应**: 动态页面监控，自动适应页面变化
- **🌐 多模型支持**: 支持OpenAI、Google Gemini、Anthropic等多种AI模型
- **📱 多标签页**: 智能标签页管理，自动切换到最相关的页面

## 📦 安装

### 自动安装（推荐）

1. **Windows 用户**:
   ```bash
   cd Plugin/SentraAutoBrowser
   install-sentra.bat
   ```

2. **Linux/Mac 用户**:
   ```bash
   cd Plugin/SentraAutoBrowser
   node install-sentra.js install
   ```

### 手动安装

1. **克隆 Sentra Auto Browser**:
   ```bash
   cd Plugin/SentraAutoBrowser
   git clone https://github.com/JustForSO/Sentra-Auto-Browser.git sentra-auto-browser
   ```

2. **安装依赖**:
   ```bash
   cd sentra-auto-browser
   npm install
   npm run build
   npx playwright install
   ```

3. **配置插件**:
   ```bash
   cd ..
   cp config.env.example config.env
   # 编辑 config.env 文件，填写你的 AI API 密钥
   ```

## ⚙️ 配置

复制 `config.env.example` 为 `config.env` 并填写配置：

```env
# AI模型配置（至少配置一个）
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

# 浏览器配置
BROWSER_HEADLESS=false
BROWSER_VIEWPORT_WIDTH=1280
BROWSER_VIEWPORT_HEIGHT=720
BROWSER_TIMEOUT=30000

# AI代理配置
MAX_STEPS=20
ENABLE_VISION=true
DEBUG_MODE=false
```

## 🎮 使用方法

### 在系统提示词中启用

在你的系统提示词中添加：
```
{{VCPSentraAutoBrowser}}
```

### 基础使用示例

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SentraAutoBrowser「末」,
task:「始」打开百度，搜索人工智能，点击第一个结果「末」
<<<[END_TOOL_REQUEST]>>>
```

### 复杂任务示例

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SentraAutoBrowser「末」,
task:「始」打开bilibili，搜索编程教程，播放播放量最高的视频，然后点赞「末」,
max_steps:「始」15「末」,
debug:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

### 电商自动化示例

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SentraAutoBrowser「末」,
task:「始」打开淘宝，搜索蓝牙耳机，筛选价格100-300元，查看评价最好的商品详情「末」,
max_steps:「始」20「末」,
provider:「始」openai「末」,
model:「始」gpt-4o「末」
<<<[END_TOOL_REQUEST]>>>
```

## 🎯 应用场景

- **🎬 视频网站自动化**: bilibili、YouTube视频搜索和互动
- **🛒 电商自动化**: 淘宝、京东购物流程自动化
- **📝 表单自动填写**: 注册、登录、数据录入
- **📊 数据采集**: 招聘信息、新闻数据等采集
- **🧪 网站测试**: 自动化功能测试

## 📋 参数说明

| 参数 | 类型 | 必需 | 说明 | 默认值 |
|------|------|------|------|--------|
| `task` | 字符串 | ✅ | 用自然语言描述要执行的浏览器任务 | - |
| `provider` | 字符串 | ❌ | AI提供商 (openai/google/anthropic) | 自动检测 |
| `model` | 字符串 | ❌ | 指定AI模型名称 | 配置文件中的默认值 |
| `headless` | 布尔值 | ❌ | 是否无头模式运行 | 配置文件中的值 |
| `max_steps` | 数字 | ❌ | 最大执行步数 | 20 |
| `enable_vision` | 布尔值 | ❌ | 是否启用视觉功能 | true |
| `debug` | 布尔值 | ❌ | 是否启用调试模式 | false |

## 🔧 故障排除

### 常见问题

1. **插件未找到**:
   - 确保已正确安装 Sentra Auto Browser
   - 检查 `sentra-auto-browser` 目录是否存在

2. **AI模型响应慢**:
   - 检查网络连接
   - 尝试更换模型或API地址
   - 使用 `gpt-4o-mini` 等快速模型

3. **浏览器启动失败**:
   - 运行 `npx playwright install` 安装浏览器
   - 尝试无头模式: `headless: true`

4. **任务执行失败**:
   - 启用调试模式: `debug: true`
   - 检查任务描述是否清晰具体
   - 增加最大步数: `max_steps`

### 检查安装状态

```bash
node install-sentra.js check
```

### 重新安装

```bash
node install-sentra.js uninstall
node install-sentra.js install
```

## 📄 许可证

本插件遵循 VCP 项目的许可证。Sentra Auto Browser 项目遵循其自身的许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 🔗 相关链接

- [Sentra Auto Browser 项目](https://github.com/JustForSO/Sentra-Auto-Browser)
- [VCP 工具箱](https://github.com/lioensky/VCPToolBox)
- [插件开发文档](../../同步插件开发手册.md)
