{"manifestVersion": "1.0.0", "name": "SentraAutoBrowser", "version": "1.0.0", "displayName": "Sentra智能浏览器自动化", "description": "基于AI驱动的智能浏览器自动化框架，支持自然语言描述的复杂网页操作任务。", "author": "VCP Team", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node sentra-browser.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "configSchema": {"OPENAI_API_KEY": {"type": "string", "description": "OpenAI API密钥，用于AI决策引擎。"}, "OPENAI_BASE_URL": {"type": "string", "description": "OpenAI API基础URL，默认为官方地址。", "default": "https://api.openai.com/v1"}, "OPENAI_MODEL": {"type": "string", "description": "使用的OpenAI模型名称。", "default": "gpt-4o-mini"}, "GOOGLE_API_KEY": {"type": "string", "description": "Google Gemini API密钥（可选）。"}, "GOOGLE_MODEL": {"type": "string", "description": "Google模型名称（可选）。", "default": "gemini-1.5-pro"}, "ANTHROPIC_API_KEY": {"type": "string", "description": "Anthropic Claude API密钥（可选）。"}, "ANTHROPIC_MODEL": {"type": "string", "description": "Anthropic模型名称（可选）。", "default": "claude-3-5-sonnet-20241022"}, "BROWSER_HEADLESS": {"type": "boolean", "description": "是否以无头模式运行浏览器。", "default": false}, "BROWSER_VIEWPORT_WIDTH": {"type": "number", "description": "浏览器视窗宽度。", "default": 1280}, "BROWSER_VIEWPORT_HEIGHT": {"type": "number", "description": "浏览器视窗高度。", "default": 720}, "BROWSER_TIMEOUT": {"type": "number", "description": "浏览器操作超时时间（毫秒）。", "default": 30000}, "MAX_STEPS": {"type": "number", "description": "最大执行步数。", "default": 20}, "ENABLE_VISION": {"type": "boolean", "description": "是否启用视觉功能。", "default": true}, "DEBUG_MODE": {"type": "boolean", "description": "是否启用调试模式。", "default": false}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "automate", "description": "执行智能浏览器自动化任务。使用自然语言描述复杂的网页操作，AI会自动分解并执行。\n\n参数:\n- task (字符串, 必需): 用自然语言描述要执行的浏览器任务\n- provider (字符串, 可选): AI提供商 (openai/google/anthropic)，默认自动检测\n- model (字符串, 可选): 指定AI模型名称\n- headless (布尔值, 可选): 是否无头模式运行\n- max_steps (数字, 可选): 最大执行步数\n- enable_vision (布尔值, 可选): 是否启用视觉功能\n- debug (布尔值, 可选): 是否启用调试模式\n\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SentraAutoBrowser「末」,\ntask:「始」打开百度，搜索人工智能，点击第一个结果「末」\n<<<[END_TOOL_REQUEST]>>>\n\n复杂任务示例:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SentraAutoBrowser「末」,\ntask:「始」打开bilibili，搜索编程教程，播放播放量最高的视频，然后点赞「末」,\nmax_steps:「始」15「末」,\ndebug:「始」true「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "<<<[TOOL_REQUEST]>>>\ntool_name:「始」SentraAutoBrowser「末」,\ntask:「始」打开淘宝，搜索蓝牙耳机，筛选价格100-300元，查看评价最好的商品「末」,\nmax_steps:「始」10「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}