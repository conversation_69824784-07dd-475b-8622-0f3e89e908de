#!/usr/bin/env node

/**
 * Sentra Auto Browser 插件测试脚本
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 测试配置
const testCases = [
    {
        name: '基础搜索测试',
        params: {
            task: '打开百度，搜索"人工智能"'
        }
    },
    {
        name: '复杂操作测试',
        params: {
            task: '打开bilibili，搜索编程教程，查看第一个视频',
            max_steps: 10,
            debug: true
        }
    }
];

// 检查安装状态
function checkInstallation() {
    const sentraPath = path.join(__dirname, 'sentra-auto-browser');
    return fs.existsSync(path.join(sentraPath, 'package.json'));
}

// 检查配置
function checkConfig() {
    const configPath = path.join(__dirname, 'config.env');
    if (!fs.existsSync(configPath)) {
        return false;
    }
    
    const config = fs.readFileSync(configPath, 'utf-8');
    return config.includes('OPENAI_API_KEY') || 
           config.includes('GOOGLE_API_KEY') || 
           config.includes('ANTHROPIC_API_KEY');
}

// 运行测试
async function runTest(testCase) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 运行测试: ${testCase.name}`);
        console.log(`📝 任务: ${testCase.params.task}`);
        
        const child = spawn('node', ['sentra-browser.js'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        // 发送测试参数
        child.stdin.write(JSON.stringify(testCase.params));
        child.stdin.end();
        
        let stdout = '';
        let stderr = '';
        
        child.stdout.on('data', (data) => {
            stdout += data.toString();
        });
        
        child.stderr.on('data', (data) => {
            stderr += data.toString();
        });
        
        child.on('close', (code) => {
            try {
                const result = JSON.parse(stdout);
                if (result.status === 'success') {
                    console.log(`✅ 测试通过: ${testCase.name}`);
                    resolve(result);
                } else {
                    console.log(`❌ 测试失败: ${testCase.name}`);
                    console.log(`错误: ${result.error}`);
                    reject(new Error(result.error));
                }
            } catch (error) {
                console.log(`❌ 测试失败: ${testCase.name}`);
                console.log(`解析错误: ${error.message}`);
                console.log(`原始输出: ${stdout}`);
                console.log(`错误输出: ${stderr}`);
                reject(error);
            }
        });
        
        // 设置超时
        setTimeout(() => {
            child.kill();
            reject(new Error('测试超时'));
        }, 60000);
    });
}

// 主测试函数
async function main() {
    console.log('🚀 Sentra Auto Browser 插件测试');
    console.log('=====================================');
    
    // 检查安装状态
    console.log('\n📋 检查安装状态...');
    if (!checkInstallation()) {
        console.log('❌ Sentra Auto Browser 未安装');
        console.log('请先运行: npm run install-sentra');
        process.exit(1);
    }
    console.log('✅ Sentra Auto Browser 已安装');
    
    // 检查配置
    console.log('\n📋 检查配置...');
    if (!checkConfig()) {
        console.log('❌ 配置文件不完整');
        console.log('请编辑 config.env 文件，填写 AI API 密钥');
        process.exit(1);
    }
    console.log('✅ 配置文件检查通过');
    
    // 运行测试用例
    console.log('\n📋 运行测试用例...');
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    for (const testCase of testCases) {
        try {
            await runTest(testCase);
            passedTests++;
        } catch (error) {
            console.log(`详细错误: ${error.message}`);
        }
    }
    
    // 输出测试结果
    console.log('\n📊 测试结果');
    console.log('=====================================');
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 所有测试通过！插件工作正常。');
        process.exit(0);
    } else {
        console.log('\n⚠️  部分测试失败，请检查配置和网络连接。');
        process.exit(1);
    }
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
    console.log('Sentra Auto Browser 插件测试脚本');
    console.log('');
    console.log('用法:');
    console.log('  node test.js          - 运行所有测试');
    console.log('  node test.js --help   - 显示帮助信息');
    process.exit(0);
}

// 运行测试
if (require.main === module) {
    main().catch(error => {
        console.error('测试运行失败:', error.message);
        process.exit(1);
    });
}
