#!/usr/bin/env node

/**
 * Sentra Auto Browser VCP Plugin
 * 基于AI驱动的智能浏览器自动化插件
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 读取配置
function loadConfig() {
    const configPath = path.join(__dirname, 'config.env');
    const config = {};
    
    if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf-8');
        const lines = configContent.split('\n');
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed && !trimmed.startsWith('#')) {
                const [key, ...valueParts] = trimmed.split('=');
                if (key && valueParts.length > 0) {
                    config[key.trim()] = valueParts.join('=').trim();
                }
            }
        }
    }
    
    return config;
}

// 检查Sentra Auto Browser是否已安装
function checkSentraInstallation() {
    const possiblePaths = [
        path.join(__dirname, 'sentra-auto-browser'),
        path.join(__dirname, '..', '..', 'sentra-auto-browser'),
        path.join(process.cwd(), 'sentra-auto-browser')
    ];

    for (const sentraPath of possiblePaths) {
        const packagePath = path.join(sentraPath, 'package.json');
        const srcPath = path.join(sentraPath, 'src');

        if (fs.existsSync(packagePath) && fs.existsSync(srcPath)) {
            return sentraPath;
        }
    }

    // 检查全局安装
    try {
        const { execSync } = require('child_process');
        execSync('npx sentra-auto --version', { stdio: 'ignore' });
        return 'global'; // 标记为全局安装
    } catch (error) {
        // 全局安装不可用
    }

    return null;
}

// 执行Sentra Auto Browser
async function executeSentraTask(task, options = {}) {
    return new Promise((resolve, reject) => {
        const sentraPath = checkSentraInstallation();

        if (!sentraPath) {
            reject(new Error('Sentra Auto Browser 未找到。请运行安装脚本: node install-sentra.js install'));
            return;
        }

        // 构建命令参数
        const args = ['sentra-auto', 'run', task];

        // 添加可选参数
        if (options.provider) {
            args.push('--provider', options.provider);
        }
        if (options.model) {
            args.push('--model', options.model);
        }
        if (options.max_steps) {
            args.push('--max-steps', options.max_steps.toString());
        }
        if (options.headless === true) {
            args.push('--headless');
        } else if (options.headless === false) {
            args.push('--visible');
        }
        if (options.enable_vision === false) {
            args.push('--no-vision');
        }
        if (options.debug === true) {
            args.push('--debug');
        }

        // 设置环境变量
        const env = { ...process.env };
        const config = loadConfig();

        // 传递配置到环境变量
        Object.keys(config).forEach(key => {
            env[key] = config[key];
        });

        // 确定工作目录和命令
        let cwd = process.cwd();
        let command = 'npx';

        if (sentraPath !== 'global') {
            cwd = sentraPath;
        }

        // 执行Sentra命令
        const sentraProcess = spawn(command, args, {
            cwd: cwd,
            env: env,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        sentraProcess.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        sentraProcess.stderr.on('data', (data) => {
            stderr += data.toString();
        });

        sentraProcess.on('close', (code) => {
            if (code === 0) {
                resolve({
                    success: true,
                    output: stdout,
                    stderr: stderr
                });
            } else {
                reject(new Error(`Sentra Auto Browser 执行失败 (退出码: ${code})\n错误信息: ${stderr}\n标准输出: ${stdout}`));
            }
        });

        sentraProcess.on('error', (error) => {
            reject(new Error(`启动 Sentra Auto Browser 失败: ${error.message}\n请确保已正确安装 Sentra Auto Browser`));
        });

        // 设置超时
        const timeout = parseInt(config.BROWSER_TIMEOUT || '120000');
        setTimeout(() => {
            sentraProcess.kill();
            reject(new Error(`任务执行超时 (${timeout}ms)。请尝试增加 BROWSER_TIMEOUT 配置或减少任务复杂度。`));
        }, timeout);
    });
}

// 主函数
async function main() {
    try {
        // 读取stdin输入
        let inputData = '';
        
        if (process.stdin.isTTY) {
            throw new Error('此插件需要通过stdin接收参数');
        }
        
        process.stdin.setEncoding('utf8');
        
        for await (const chunk of process.stdin) {
            inputData += chunk;
        }
        
        if (!inputData.trim()) {
            throw new Error('未接收到输入参数');
        }
        
        // 解析输入参数
        const params = JSON.parse(inputData.trim());
        
        if (!params.task) {
            throw new Error('缺少必需参数: task');
        }
        
        // 准备执行选项
        const config = loadConfig();
        const options = {
            provider: params.provider || (config.OPENAI_API_KEY ? 'openai' : 
                     config.GOOGLE_API_KEY ? 'google' : 
                     config.ANTHROPIC_API_KEY ? 'anthropic' : null),
            model: params.model || config.OPENAI_MODEL || config.GOOGLE_MODEL || config.ANTHROPIC_MODEL,
            max_steps: params.max_steps || parseInt(config.MAX_STEPS || '20'),
            headless: params.headless !== undefined ? params.headless : 
                     config.BROWSER_HEADLESS === 'true',
            enable_vision: params.enable_vision !== undefined ? params.enable_vision : 
                          config.ENABLE_VISION !== 'false',
            debug: params.debug !== undefined ? params.debug : 
                   config.DEBUG_MODE === 'true'
        };
        
        // 执行任务
        const result = await executeSentraTask(params.task, options);
        
        // 返回结果
        const response = {
            status: 'success',
            result: `浏览器自动化任务执行完成！\n\n任务描述: ${params.task}\n\n执行结果:\n${result.output}`,
            messageForAI: `已成功执行浏览器自动化任务："${params.task}"。任务已完成，请查看执行结果。`
        };
        
        console.log(JSON.stringify(response, null, 2));
        
    } catch (error) {
        const errorResponse = {
            status: 'error',
            error: error.message,
            messageForAI: `浏览器自动化任务执行失败: ${error.message}`
        };
        
        console.log(JSON.stringify(errorResponse, null, 2));
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { executeSentraTask, loadConfig };
